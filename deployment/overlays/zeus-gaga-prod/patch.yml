apiVersion: apps/v1
kind: Deployment
metadata:
  name: zeus-deployment
spec:
  replicas: 2
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        armsPilotAutoEnable: "on"
        armsPilotCreateAppName: "prod-zeus-gaga"
        aliyun.com/agent-version: 3.2.10
    spec:
      containers:
        - name: main
      volumes:
        - name: config-volume
          configMap:
            name: prod-zeus-platform-config
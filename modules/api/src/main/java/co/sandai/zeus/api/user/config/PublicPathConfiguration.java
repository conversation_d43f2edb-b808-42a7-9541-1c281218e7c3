package co.sandai.zeus.api.user.config;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;

/**
 * Configuration for public paths that don't require authentication.
 * Uses Spring's mature AntPathMatcher for robust URL pattern matching.
 * Supports HTTP method-specific configuration.
 */
@Slf4j
@Component
public class PublicPathConfiguration {

    /**
     * -- GETTER --
     *  Get the underlying AntPathMatcher instance for advanced usage.
     *
     */
    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    /**
     * HTTP method-specific public paths configuration
     */
    private final Map<String, List<String>> methodSpecificPaths = new HashMap<String, List<String>>() {
        {
            // GET requests that are public
            put(
                    "GET",
                    List.of(
                            "/api/v1/assets" // Allow anonymous users to list assets with restrictions
                    ));
        }
    };

    /**
     * List of public paths that don't require authentication.
     * Supports Ant-style patterns:
     * - ? matches one character
     * - * matches zero or more characters (excluding path separators)
     * - ** matches zero or more path segments
     * - {variable} matches path variables
     */
    private final List<String> paths = Arrays.asList(
            "/",
            "/api/v1/tasks/{taskId}/chunks.m3u8",
            "/api/v1/tasks/{taskId}",
            "/api/v1/share/{shareId}",
            "/api/v1/share/{shareId}/preview",
            "/api/v1/user/signup",
            "/api/v1/user/login",
            "/api/v1/user/send-reset-password-email",
            "/api/v1/user/reset-password",
            "/api/v1/user/verify-email",
            "/api/v1/user/send-verify-email",
            "/api/v1/plans",
            "/api/v1/credits/package",
            "/api/v1/assets/mget",
            "/api/v1/assets/video-moderation-callback",
            "/api/v1/infer/events",
            "/auth/**",
            "/v3/api-docs/**",
            "/swagger-ui.html",
            "/actuator/prometheus",
            "/actuator/readiness",
            "/payment/stripe/webhook",
            "/payment/sessions/checkout",
            "/api/v1/generations/streaming-download", // 采用手动鉴权
            "/swagger-ui/**",
            "/api/v1/voices" // voices接口免鉴权
            );

    /**
     * Check if the given request path matches any of the configured public paths.
     * Uses Spring's AntPathMatcher for robust pattern matching.
     *
     * @param requestPath the request path to check
     * @return true if the path is public, false otherwise
     */
    public boolean isPublicPath(String requestPath) {
        if (requestPath == null) {
            return false;
        }

        boolean isPublic = paths.stream().anyMatch(pattern -> pathMatcher.match(pattern, requestPath));

        if (log.isDebugEnabled()) {
            log.debug("Checking path '{}' against public patterns: {}", requestPath, isPublic);
        }

        return isPublic;
    }

    /**
     * Check if the given request path and HTTP method combination is public.
     * First checks method-specific paths, then falls back to general public paths.
     *
     * @param requestPath the request path to check
     * @param httpMethod the HTTP method (GET, POST, PUT, DELETE, etc.)
     * @return true if the path is public for this method, false otherwise
     */
    public boolean isPublicPath(String requestPath, String httpMethod) {
        if (requestPath == null || httpMethod == null) {
            return false;
        }

        // First check method-specific paths
        List<String> methodPaths = methodSpecificPaths.get(httpMethod.toUpperCase());
        if (methodPaths != null) {
            boolean isMethodSpecificPublic =
                    methodPaths.stream().anyMatch(pattern -> pathMatcher.match(pattern, requestPath));
            if (isMethodSpecificPublic) {
                if (log.isDebugEnabled()) {
                    log.debug("Path '{}' with method '{}' is public (method-specific)", requestPath, httpMethod);
                }
                return true;
            }
        }

        // Fall back to general public paths
        boolean isGeneralPublic = isPublicPath(requestPath);

        if (log.isDebugEnabled()) {
            log.debug("Path '{}' with method '{}' public status: {}", requestPath, httpMethod, isGeneralPublic);
        }

        return isGeneralPublic;
    }
}
